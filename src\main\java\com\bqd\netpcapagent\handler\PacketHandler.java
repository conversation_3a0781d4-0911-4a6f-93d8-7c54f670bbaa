package com.bqd.netpcapagent.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.pcap4j.core.PacketListener;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-03-03
 */
public class PacketHandler implements PacketListener {

    private static final PacketHandler packetHandler = new PacketHandler();
    public static final Map<Long, TcpSession> reqtPacketMap = new ConcurrentHashMap<>();
    public static final Map<Long, TcpSession> respPacketMap = new ConcurrentHashMap<>();

    private String hostIp = "0.0.0.0";

    //单例模式
    private PacketHandler() {
    }

    //获取实例
    public static PacketHandler getInstance() {
        return packetHandler;
    }

    //设置hostIp
    public PacketHandler setHostIp(String hostIp) {
        packetHandler.hostIp = hostIp;
        return packetHandler;
    }

    @Override
    public void gotPacket(Packet packet) {
        //获取tcp报文
        TcpPacket tcpPacket = packet.get(TcpPacket.class);
        //若报文或负载为空，则返回
        if (ObjectUtil.isNull(tcpPacket) || ObjectUtil.isNull(tcpPacket.getPayload())) {
            return;
        }
        //获取ipv4报文
        IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
        //报文目标ip与主机ip相等，则为请求
        if (StrUtil.equals(ipV4Packet.getHeader().getDstAddr().getHostAddress(), hostIp)) {
            addPacketToMap(reqtPacketMap, tcpPacket);
            return;
        }
        //否则则为响应
        addPacketToMap(respPacketMap, tcpPacket);
    }

    /**
     * 处理请求报文
     * @param tcpPacket
     */
    private void addPacketToMap(Map<Long, TcpSession> map, TcpPacket tcpPacket) {
        //获取报文确认码
        long ackNo = tcpPacket.getHeader().getAcknowledgmentNumberAsLong();
        //根据确认码尝试获取map中的数据
        TcpSession tcpSession = map.get(ackNo);
        //map中无数据，则创建一个tcpSession
        if (ObjectUtil.isNull(tcpSession)) {
            tcpSession = new TcpSession(new ArrayList<>(), new Date());
            map.put(ackNo, tcpSession);
        }
        tcpSession.getTcpPacketList().add(tcpPacket);
    }

    @Data
    @AllArgsConstructor
    public static final class TcpSession {
        private List<TcpPacket> tcpPacketList;
        private Date createTime;
    }

}
