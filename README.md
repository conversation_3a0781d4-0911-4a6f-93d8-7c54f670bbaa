# 网络数据包捕获代理

一个高性能、生产就绪的网络数据包捕获代理，用于捕获TCP数据包，将其重组为HTTP请求/响应，并将数据发送到Apache RocketMQ进行进一步处理。

## 功能特性

- **实时数据包捕获**: 在指定的网络接口和端口上捕获TCP数据包
- **HTTP重组**: 智能地将TCP数据包重组为完整的HTTP请求和响应
- **消息队列集成**: 将重组的HTTP数据发送到Apache RocketMQ
- **Web控制面板**: 用户友好的Web界面，用于监控和控制
- **RESTful API**: 用于程序化控制的综合REST API
- **性能优化**: 具有可配置性能调优的多线程处理
- **生产就绪**: 全面的日志记录、监控和错误处理
- **简易部署**: 基于JAR的简单部署，配有启动脚本
- **配置管理**: 通过YAML文件和环境变量进行灵活配置

## 快速开始

### 前置条件

- Java 8或更高版本
- Maven 3.6+
- Apache RocketMQ服务器（单独运行）
- 具有适当数据包捕获权限的网络接口

### 方式一: 使用启动脚本（推荐）

1. 克隆仓库:
```bash
git clone <repository-url>
cd net-pcap-agent
```

2. 使用默认配置构建并启动:
```bash
./start.sh --build
```

3. 或使用自定义配置启动:
```bash
./start.sh --host-ip ************* --port 8080 --rocketmq-server *************:9876
```

4. 访问控制面板:
- Web控制面板: http://localhost:25900/agent/net-pcap/dashboard
- API文档: http://localhost:25900/agent/net-pcap/api/v1

### 方式二: 手动JAR执行

1. 构建应用程序:
```bash
mvn clean package
```

2. 在`application.yml`中配置RocketMQ连接或通过环境变量:
```bash
export ROCKETMQ_NAME_SERVER=localhost:9876
export HOST_IP=*************
```

3. 运行应用程序:
```bash
java -jar target/net-pcap-agent-0.0.1-SNAPSHOT.jar
```

### 方式三: 后台服务

将应用程序作为后台服务启动:
```bash
./start.sh --background --host-ip ************* --port 8080
```

停止后台服务:
```bash
# 从net-pcap-agent.pid文件中查找PID
kill $(cat net-pcap-agent.pid) && rm net-pcap-agent.pid
```

## 配置

### 启动脚本选项

`start.sh` 脚本支持以下选项:

| 选项 | 描述 | 默认值 |
|--------|-------------|---------|
| `--host-ip IP` | 用于区分请求/响应数据包的主机IP | 127.0.0.1 |
| `--port PORT` | 默认捕获端口 | 80 |
| `--rocketmq-server SERVER` | RocketMQ服务器地址 | localhost:9876 |
| `--worker-threads THREADS` | 工作线程数 | 4 |
| `--profile PROFILE` | Spring配置文件 (dev/prod) | prod |
| `--build` | 启动前构建 | false |
| `--dev` | 以开发模式启动 | false |
| `--background` | 作为后台服务启动 | false |
| `--jvm-opts OPTS` | 额外的JVM选项 | - |

### 环境变量

| 变量 | 默认值 | 描述 |
|----------|---------|-------------|
| `HOST_IP` | 127.0.0.1 | 用于区分请求/响应数据包的主机IP |
| `ROCKETMQ_NAME_SERVER` | localhost:9876 | RocketMQ NameServer地址 |
| `DEFAULT_PORT` | 80 | 默认捕获端口 |
| `WORKER_THREADS` | 4 | 数据包处理的工作线程数 |
| `BUFFER_MAX_SIZE` | 10000 | 缓冲的最大数据包数 |
| `REASSEMBLY_CONFIRM_DURATION` | 5 | 确认持续时间（秒） |

### Application Configuration

The application can be configured via `application.yml`:

```yaml
packet-capture:
  host-ip: *************
  default-port: 8080
  buffer:
    max-size: 10000
    cleanup-interval: 60
    max-age: 300
  reassembly:
    default-confirm-duration: 5
    max-wait-time: 30
    batch-size: 100
  performance:
    worker-threads: 4
    pcap-buffer-size: 65536
    pcap-timeout: 1000
```

### Profile-Specific Configuration

Use different profiles for different environments:

**Development Profile** (`--dev` or `--profile dev`):
- Debug logging enabled
- Smaller buffer sizes
- Faster processing for development

**Production Profile** (`--profile prod`):
- Optimized for performance
- Larger buffers
- Conservative error handling

## API Reference

### Start Packet Capture
```http
POST /api/v1/capture/start
Content-Type: application/x-www-form-urlencoded

deviceName=eth0&port=8080
```

### Stop Packet Capture
```http
POST /api/v1/capture/stop
```

### Get Capture Status
```http
GET /api/v1/capture/status
```

### Get Processing Statistics
```http
GET /api/v1/stats
```

### Get Network Interfaces
```http
GET /api/v1/interfaces
```

## Web Dashboard

The web dashboard provides:

- **Real-time monitoring** of packet capture status
- **Performance metrics** and statistics
- **Control panel** for starting/stopping capture
- **Configuration management**
- **Session monitoring** for request/response pairs

Access the dashboard at: `http://localhost:25900/agent/net-pcap/dashboard`

## Deployment

### Production Deployment

1. **Build the application**:
```bash
mvn clean package -Dmaven.test.skip=true
```

2. **Create a deployment directory**:
```bash
mkdir -p /opt/net-pcap-agent
cp target/net-pcap-agent-*.jar /opt/net-pcap-agent/
cp start.sh /opt/net-pcap-agent/
chmod +x /opt/net-pcap-agent/start.sh
```

3. **Create configuration file**:
```bash
# Create application-prod.yml with production settings
cp src/main/resources/application-prod.yml /opt/net-pcap-agent/
```

4. **Start the service**:
```bash
cd /opt/net-pcap-agent
./start.sh --profile prod --background --host-ip <your-host-ip> --rocketmq-server <rocketmq-server>
```

### Service Management

Create a systemd service for automatic startup:

```bash
# Create /etc/systemd/system/net-pcap-agent.service
[Unit]
Description=Network Packet Capture Agent
After=network.target

[Service]
Type=forking
User=pcap-user
WorkingDirectory=/opt/net-pcap-agent
ExecStart=/opt/net-pcap-agent/start.sh --background --profile prod
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/net-pcap-agent/net-pcap-agent.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl enable net-pcap-agent
sudo systemctl start net-pcap-agent
sudo systemctl status net-pcap-agent
```

## Architecture

### Components

1. **PacketCaptureService**: Manages network interface capture using pcap4j
2. **PacketProcessingService**: Processes and reassembles TCP packets
3. **RocketMQ Integration**: Sends reassembled HTTP data to message queue
4. **Web Dashboard**: Provides user interface for monitoring and control
5. **REST API**: Enables programmatic control and monitoring

### Data Flow

1. **Capture**: TCP packets are captured from specified network interface
2. **Classification**: Packets are classified as requests or responses based on destination IP
3. **Buffering**: Packets are buffered in thread-safe collections
4. **Reassembly**: TCP packets are reassembled into HTTP requests/responses
5. **Publishing**: Complete HTTP pairs are sent to RocketMQ

## Monitoring

### Health Checks

- Application health: `/actuator/health`
- Metrics: `/actuator/metrics`
- Prometheus metrics: `/actuator/prometheus`

### Logging

Logs are written to:
- Console (configurable level)
- File: `logs/net-pcap-agent.log` (with rotation)

### Performance Metrics

- Processed packets count
- Reassembled sessions count
- Sent messages count
- Error count
- Current buffer size
- Dropped packets count

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the application has sufficient privileges for packet capture
   ```bash
   # Run with sudo if needed
   sudo ./start.sh --host-ip *************
   ```

2. **No Network Interfaces**: Check if pcap libraries are properly installed
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install libpcap-dev

   # On CentOS/RHEL
   sudo yum install libpcap-devel
   ```

3. **RocketMQ Connection**: Verify RocketMQ is running and accessible
   ```bash
   # Test connection
   telnet <rocketmq-host> 9876
   ```

4. **High Memory Usage**: Adjust buffer sizes and cleanup intervals
   ```bash
   ./start.sh --jvm-opts "-Xmx1g -XX:+UseG1GC" --host-ip *************
   ```

5. **Application Already Running**: Check for existing processes
   ```bash
   # Check if PID file exists
   cat net-pcap-agent.pid

   # Kill existing process
   kill $(cat net-pcap-agent.pid) && rm net-pcap-agent.pid
   ```

### Performance Tuning

- **High Traffic**: Increase worker threads and buffer sizes
  ```bash
  ./start.sh --worker-threads 8 --jvm-opts "-Xmx2g"
  ```

- **Memory Optimization**: Tune JVM garbage collection
  ```bash
  ./start.sh --jvm-opts "-Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
  ```

- **Network Optimization**: Adjust pcap buffer size in configuration
  ```yaml
  packet-capture:
    performance:
      pcap-buffer-size: 131072  # Larger buffer for high traffic
  ```

### Log Analysis

View application logs:
```bash
# Real-time logs
tail -f logs/net-pcap-agent.log

# Background service logs
tail -f logs/application.log

# Search for errors
grep ERROR logs/net-pcap-agent.log
```

## Development

### Building from Source

```bash
git clone <repository-url>
cd net-pcap-agent
mvn clean package
```

### Development Mode

Start in development mode with debug logging:
```bash
./start.sh --dev --build
```

### Running Tests

```bash
mvn test
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details
- Use the web dashboard for real-time monitoring
