package com.bqd.netpcapagent.health;

import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * 数据包捕获功能的健康指示器
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Component
public class PacketCaptureHealthIndicator implements HealthIndicator {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @Override
    public Health health() {
        try {
            Health.Builder builder = new Health.Builder();

            // 检查捕获服务状态
            boolean captureRunning = packetCaptureService.isCaptureRunning();
            boolean reassemblyRunning = packetProcessingService.isReassemblyRunning();

            // 获取当前统计信息
            PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();

            // 确定整体健康状态
            if (captureRunning && reassemblyRunning) {
                builder.up();
            } else if (!captureRunning && !reassemblyRunning) {
                // 两者都停止 - 这可能是有意的
                builder.up().withDetail("status", "stopped");
            } else {
                // 不一致状态
                builder.down().withDetail("status", "inconsistent");
            }

            // 添加详细信息
            builder.withDetail("captureRunning", captureRunning)
                   .withDetail("reassemblyRunning", reassemblyRunning)
                   .withDetail("processedPackets", stats.getProcessedPackets())
                   .withDetail("reassembledSessions", stats.getReassembledSessions())
                   .withDetail("sentMessages", stats.getSentMessages())
                   .withDetail("errorCount", stats.getErrorCount())
                   .withDetail("currentBufferSize", stats.getCurrentBufferSize())
                   .withDetail("droppedPackets", stats.getDroppedPackets());

            // 如果捕获正在运行，添加当前配置
            PacketCaptureService.CaptureConfig config = packetCaptureService.getCurrentConfig();
            if (config != null) {
                builder.withDetail("currentDevice", config.getDeviceName())
                       .withDetail("currentPort", config.getPort())
                       .withDetail("captureStartTime", config.getStartTime())
                       .withDetail("capturedPackets", config.getCapturedPackets());
            }

            // 检查潜在问题
            if (stats.getErrorCount() > 0) {
                builder.withDetail("warning", "处理过程中检测到错误");
            }

            if (stats.getDroppedPackets() > 0) {
                builder.withDetail("warning", "由于缓冲区溢出，数据包已被丢弃");
            }

            return builder.build();

        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", "检查数据包捕获健康状态失败")
                    .withDetail("exception", e.getMessage())
                    .build();
        }
    }
}
