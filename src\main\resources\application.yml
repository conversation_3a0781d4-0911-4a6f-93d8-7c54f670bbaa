server:
  port: 25900
  servlet:
    context-path: /agent/net-pcap

spring:
  application:
    name: net-pcap-agent
  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false

# Logging configuration
logging:
  level:
    com.bqd.netpcapagent: INFO
    org.pcap4j: WARN
    org.apache.rocketmq: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/net-pcap-agent.log
    max-size: 100MB
    max-history: 30

# RocketMQ configuration
rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:net-pcap-producer}
    send-message-timeout: ${ROCKETMQ_SEND_TIMEOUT:5000}
    retry-times-when-send-failed: ${ROCKETMQ_RETRY_TIMES:3}
    retry-times-when-send-async-failed: ${ROCKETMQ_ASYNC_RETRY_TIMES:3}
    max-message-size: ${ROCKETMQ_MAX_MESSAGE_SIZE:4194304}
  # Connection pool settings for standalone deployment
  consumer:
    pull-thread-nums: ${ROCKETMQ_CONSUMER_THREADS:4}
    consume-thread-min: ${ROCKETMQ_CONSUME_THREAD_MIN:2}
    consume-thread-max: ${ROCKETMQ_CONSUME_THREAD_MAX:8}

# Packet capture configuration
packet-capture:
  # Host IP for distinguishing request/response packets
  host-ip: ${HOST_IP:127.0.0.1}

  # Default network interface (can be overridden via API)
  default-interface: ${DEFAULT_INTERFACE:}

  # Default port to capture (can be overridden via API)
  default-port: ${DEFAULT_PORT:80}

  # Packet buffer configuration
  buffer:
    # Maximum number of packets to buffer before processing
    max-size: ${BUFFER_MAX_SIZE:10000}
    # Buffer cleanup interval in seconds
    cleanup-interval: ${BUFFER_CLEANUP_INTERVAL:60}
    # Maximum age of packets in buffer before cleanup (seconds)
    max-age: ${BUFFER_MAX_AGE:300}

  # HTTP reassembly configuration
  reassembly:
    # Default confirmation duration in seconds
    default-confirm-duration: ${REASSEMBLY_CONFIRM_DURATION:5}
    # Maximum time to wait for packet reassembly (seconds)
    max-wait-time: ${REASSEMBLY_MAX_WAIT:30}
    # Batch size for processing packets
    batch-size: ${REASSEMBLY_BATCH_SIZE:100}

  # Performance tuning
  performance:
    # Number of worker threads for packet processing
    worker-threads: ${WORKER_THREADS:4}
    # Pcap buffer size
    pcap-buffer-size: ${PCAP_BUFFER_SIZE:65536}
    # Pcap timeout in milliseconds
    pcap-timeout: ${PCAP_TIMEOUT:1000}

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true