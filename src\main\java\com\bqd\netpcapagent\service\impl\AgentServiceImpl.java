package com.bqd.netpcapagent.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bqd.netpcapagent.handler.PacketHandler;
import com.bqd.netpcapagent.model.HttpPacketDto;
import com.bqd.netpcapagent.service.AgentService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.pcap4j.core.*;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-28
 */
@Service
public class AgentServiceImpl implements AgentService {

    public static boolean status = false;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private ApplicationArguments applicationArguments;

    /**
     * 获取网卡列表
     * @return
     */
    @Override
    public List<String> deviceList() {
        try {
            return Pcaps.findAllDevs().stream().map(PcapNetworkInterface::getName).collect(Collectors.toList());
        } catch (PcapNativeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 开始抓包
     * @param deviceName
     * @param port
     */
    @Override
    public void capture(String deviceName, String port) {
        status = true;
        new Thread(() -> {
            try {
                PcapNetworkInterface pcapNetworkInterface = Pcaps.getDevByName(deviceName);
                if (pcapNetworkInterface == null) {
                    throw new RuntimeException("网络设备不存在");
                }
                PcapHandle pcapHandle = pcapNetworkInterface.openLive(65536, PcapNetworkInterface.PromiscuousMode.PROMISCUOUS, 1000);
                pcapHandle.setFilter("tcp port " + port, BpfProgram.BpfCompileMode.OPTIMIZE);
                PacketHandler packetHandler = PacketHandler.getInstance().setHostIp(applicationArguments.getOptionValues("hostIp").get(0));
                while (status) {
                    Packet packet = pcapHandle.getNextPacket();
                    if (ObjectUtil.isNull(packet)) {
                        continue;
                    }
                    packetHandler.gotPacket(packet);
                }
                pcapHandle.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).start();
    }

    /**
     * 将tcp包重组成http包并发送到mq
     * @param confirmDuration 确认时长，超出该时长的报文视为回复完成，进行重组
     */
    @Override
    public void reassemblePacketAndSend(Long confirmDuration) {
        new Thread(() -> {
            while (status || !PacketHandler.respPacketMap.isEmpty()) {
                Date nowDate = new Date();
                Iterator<Map.Entry<Long, PacketHandler.TcpSession>> iterator = PacketHandler.respPacketMap.entrySet().iterator();
                //遍历respMap
                while (iterator.hasNext()) {
                    Map.Entry<Long, PacketHandler.TcpSession> entry = iterator.next();
                    PacketHandler.TcpSession respTcpSession = entry.getValue();

                    Date respTime = respTcpSession.getCreateTime();
                    if (DateUtil.between(respTime, nowDate, DateUnit.SECOND) < confirmDuration) {
                        continue;
                    }

                    iterator.remove();

                    //处理请求
                    PacketHandler.TcpSession reqtTcpSession = getReqtTcpSession(respTcpSession.getTcpPacketList().get(0).getHeader().getSequenceNumberAsLong());
                    if (ObjectUtil.isNull(reqtTcpSession)) {
                        continue;
                    }
                    String request = reassembleToHttp(reqtTcpSession.getTcpPacketList());

                    String response = reassembleToHttp(respTcpSession.getTcpPacketList());

                    if (StrUtil.hasBlank(request, response)) {
                        continue;
                    }

                    rocketMQTemplate.sendOneWay("net-pcap-agent", new HttpPacketDto(request, reqtTcpSession.getCreateTime().getTime(), response, respTime.getTime()));
                }
            }
        }).start();
    }

    /**
     * 重组http请求tcp包
     * @param ackNo
     * @return
     */
    private PacketHandler.TcpSession getReqtTcpSession (long ackNo) {
        PacketHandler.TcpSession tcpSession = PacketHandler.reqtPacketMap.remove(ackNo);
        if (ObjectUtil.isNull(tcpSession)) {
            return null;
        }
        return tcpSession;
    }

    /**
     * tcp报文重组成http
     * @param tcpPacketList
     * @return
     */
    private String reassembleToHttp(List<TcpPacket> tcpPacketList) {
        StringBuilder stringBuilder = new StringBuilder();
        tcpPacketList.stream().sorted((t1, t2) -> {
            if (t1.getHeader().getSequenceNumberAsLong() > t2.getHeader().getSequenceNumberAsLong()) {
                return 1;
            } else if (t1.getHeader().getSequenceNumberAsLong() < t2.getHeader().getSequenceNumberAsLong()) {
                return -1;
            }
            return 0;
        }).forEach(tcpPacket -> stringBuilder.append(new String(tcpPacket.getPayload().getRawData())));
        return stringBuilder.toString();
    }

}
