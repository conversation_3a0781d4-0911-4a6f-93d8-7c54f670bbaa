2025-05-29 13:38:05 [main] INFO  c.b.n.NetPcapAgentApplication - Starting NetPcapAgentApplication using Java 1.8.0_431 on zirui-laptop with PID 2656 (E:\dev_project\net-pcap-agent\target\classes started by zirui in E:\dev_project\net-pcap-agent)
2025-05-29 13:38:05 [main] INFO  c.b.n.NetPcapAgentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-29 13:38:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$EnhancerBySpringCGLIB$$6606d921] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:38:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$EnhancerBySpringCGLIB$$ecce285] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:38:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:38:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:38:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 13:38:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 25900 (http)
2025-05-29 13:38:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-29 13:38:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-05-29 13:38:06 [main] INFO  o.a.c.c.C.[.[.[/agent/net-pcap] - Initializing Spring embedded WebApplicationContext
2025-05-29 13:38:06 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 906 ms
2025-05-29 13:38:07 [main] INFO  o.a.r.s.a.RocketMQAutoConfiguration - a producer (net-pcap-producer) init on namesrv localhost:9876
2025-05-29 13:38:09 [main] INFO  c.b.n.s.i.PacketProcessingServiceImpl - Packet processing service initialized with 4 worker threads
2025-05-29 13:38:09 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-29 13:38:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 25900 (http) with context path '/agent/net-pcap'
2025-05-29 13:38:09 [main] INFO  c.b.n.NetPcapAgentApplication - Started NetPcapAgentApplication in 4.299 seconds (JVM running for 4.916)
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - === 网络数据包捕获代理已启动 ===
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 部署模式: 基于JAR的独立部署
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 主机IP: 127.0.0.1
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 默认网络接口: 
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 默认端口: 80
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 工作线程数: 4
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 缓冲区最大大小: 10000
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - Pcap缓冲区大小: 65536
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 访问地址:
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig -   控制面板: http://localhost:25900/agent/net-pcap/dashboard
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig -   REST API: http://localhost:25900/agent/net-pcap/api/v1
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig -   健康检查: http://localhost:25900/agent/net-pcap/actuator/health
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig -   指标监控: http://localhost:25900/agent/net-pcap/actuator/metrics
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - 使用Web控制面板启动数据包捕获或使用REST API
2025-05-29 13:38:09 [main] INFO  c.b.n.c.ApplicationStartupConfig - ===========================================
2025-05-29 13:38:28 [http-nio-25900-exec-1] INFO  o.a.c.c.C.[.[.[/agent/net-pcap] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:38:28 [http-nio-25900-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-29 13:38:28 [http-nio-25900-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-29 13:38:28 [http-nio-25900-exec-1] ERROR c.b.n.e.GlobalExceptionHandler - 意外错误: Handler dispatch failed; nested exception is java.lang.UnsatisfiedLinkError: Unable to load library 'wpcap':
找不到指定的模块。

找不到指定的模块。

找不到指定的模块。

Native library (win32-x86-64/wpcap.dll) not found in resource path ([])
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.UnsatisfiedLinkError: Unable to load library 'wpcap':
找不到指定的模块。

找不到指定的模块。

找不到指定的模块。

Native library (win32-x86-64/wpcap.dll) not found in resource path ([])
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1086)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.UnsatisfiedLinkError: Unable to load library 'wpcap':
找不到指定的模块。

找不到指定的模块。

找不到指定的模块。

Native library (win32-x86-64/wpcap.dll) not found in resource path ([])
	at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:302)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:455)
	at com.sun.jna.NativeLibrary.getInstance(NativeLibrary.java:397)
	at com.sun.jna.Function.getFunction(Function.java:102)
	at org.pcap4j.core.NativeMappings.<clinit>(NativeMappings.java:44)
	at org.pcap4j.core.Pcaps.findAllDevs(Pcaps.java:56)
	at com.bqd.netpcapagent.service.impl.PacketCaptureServiceImpl.getNetworkInterfaces(PacketCaptureServiceImpl.java:46)
	at com.bqd.netpcapagent.controller.DashboardController.dashboard(DashboardController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	... 43 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: 找不到指定的模块。

		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:191)
		... 61 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: 找不到指定的模块。

		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:204)
		... 61 common frames omitted
	Suppressed: java.lang.UnsatisfiedLinkError: 找不到指定的模块。

		at com.sun.jna.Native.open(Native Method)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:265)
		... 61 common frames omitted
	Suppressed: java.io.IOException: Native library (win32-x86-64/wpcap.dll) not found in resource path ([])
		at com.sun.jna.Native.extractFromResourcePath(Native.java:1095)
		at com.sun.jna.NativeLibrary.loadLibrary(NativeLibrary.java:276)
		... 61 common frames omitted
2025-05-29 13:38:48 [SpringApplicationShutdownHook] WARN  c.b.n.s.i.PacketCaptureServiceImpl - Packet capture is not running
2025-05-29 13:38:48 [SpringApplicationShutdownHook] INFO  c.b.n.s.i.PacketProcessingServiceImpl - Packet processing service destroyed
