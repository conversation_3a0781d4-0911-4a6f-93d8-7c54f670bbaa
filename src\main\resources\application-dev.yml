# Development environment configuration
spring:
  profiles:
    active: dev

# Enhanced logging for development
logging:
  level:
    com.bqd.netpcapagent: DEBUG
    org.pcap4j: DEBUG
    org.apache.rocketmq: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development-specific packet capture settings
packet-capture:
  host-ip: ${HOST_IP:127.0.0.1}
  default-port: ${DEFAULT_PORT:8080}
  
  buffer:
    max-size: 1000  # Smaller buffer for development
    cleanup-interval: 30
    max-age: 120
  
  reassembly:
    default-confirm-duration: 2  # Faster processing for development
    max-wait-time: 15
    batch-size: 50
  
  performance:
    worker-threads: 2  # Fewer threads for development
    pcap-buffer-size: 32768
    pcap-timeout: 500

# Development RocketMQ settings
rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:net-pcap-dev}
    send-message-timeout: 10000  # Longer timeout for development
    retry-times-when-send-failed: 1  # Fewer retries for faster feedback

# Enable all actuator endpoints for development
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    configprops:
      show-values: always
