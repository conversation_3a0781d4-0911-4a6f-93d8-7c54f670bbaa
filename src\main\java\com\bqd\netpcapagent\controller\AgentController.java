package com.bqd.netpcapagent.controller;

import com.bqd.netpcapagent.model.TcpSession;
import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据包捕获操作的REST API控制器
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Slf4j
@RestController
@RequestMapping("/api/v1")
@Validated
public class AgentController {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    /**
     * 获取可用网络接口列表
     *
     * @return 网络接口列表
     */
    @GetMapping("/interfaces")
    public ResponseEntity<List<PacketCaptureService.NetworkInterface>> getNetworkInterfaces() {
        List<PacketCaptureService.NetworkInterface> interfaces = packetCaptureService.getNetworkInterfaces();
        return ResponseEntity.ok(interfaces);
    }

    /**
     * 启动数据包捕获
     *
     * @param deviceName 网络接口名称
     * @param port 要捕获的端口号
     * @return 成功响应
     */
    @PostMapping("/capture/start")
    public ResponseEntity<Map<String, Object>> startCapture(
            @RequestParam @NotBlank(message = "设备名称不能为空") String deviceName,
            @RequestParam @Min(value = 1, message = "端口必须大于0")
                         @Max(value = 65535, message = "端口必须小于65536") int port) {

        packetCaptureService.startCapture(deviceName, port);
        packetProcessingService.startReassemblyProcess();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "数据包捕获启动成功");
        response.put("deviceName", deviceName);
        response.put("port", port);
        response.put("timestamp", System.currentTimeMillis());

        log.info("数据包捕获已在设备 {} 端口 {} 上启动", deviceName, port);
        return ResponseEntity.ok(response);
    }

    /**
     * 停止数据包捕获
     *
     * @return 成功响应
     */
    @PostMapping("/capture/stop")
    public ResponseEntity<Map<String, Object>> stopCapture() {
        packetCaptureService.stopCapture();
        packetProcessingService.stopReassemblyProcess();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "数据包捕获停止成功");
        response.put("timestamp", System.currentTimeMillis());

        log.info("数据包捕获已停止");
        return ResponseEntity.ok(response);
    }

    /**
     * 获取捕获状态和配置
     *
     * @return 捕获状态信息
     */
    @GetMapping("/capture/status")
    public ResponseEntity<Map<String, Object>> getCaptureStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("captureRunning", packetCaptureService.isCaptureRunning());
        status.put("reassemblyRunning", packetProcessingService.isReassemblyRunning());
        status.put("currentConfig", packetCaptureService.getCurrentConfig());
        status.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(status);
    }

    /**
     * 获取处理统计信息
     *
     * @return 处理统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<PacketProcessingService.ProcessingStats> getProcessingStats() {
        PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取当前请求会话
     *
     * @return 请求会话列表
     */
    @GetMapping("/sessions/requests")
    public ResponseEntity<List<TcpSession>> getRequestSessions() {
        List<TcpSession> sessions = packetProcessingService.getRequestSessions();
        return ResponseEntity.ok(sessions);
    }

    /**
     * 获取当前响应会话
     *
     * @return 响应会话列表
     */
    @GetMapping("/sessions/responses")
    public ResponseEntity<List<TcpSession>> getResponseSessions() {
        List<TcpSession> sessions = packetProcessingService.getResponseSessions();
        return ResponseEntity.ok(sessions);
    }

    /**
     * 清空所有数据包缓冲区
     *
     * @return 成功响应
     */
    @PostMapping("/sessions/clear")
    public ResponseEntity<Map<String, Object>> clearSessions() {
        packetProcessingService.clearBuffers();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "数据包缓冲区清空成功");
        response.put("timestamp", System.currentTimeMillis());

        log.info("数据包缓冲区已清空");
        return ResponseEntity.ok(response);
    }
}
