package com.bqd.netpcapagent.service;

import java.util.List;

/**
 * 数据包捕获操作的服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
public interface PacketCaptureService {

    /**
     * 获取可用网络接口列表
     *
     * @return 网络接口名称列表
     */
    List<NetworkInterface> getNetworkInterfaces();

    /**
     * 在指定接口和端口上启动数据包捕获
     *
     * @param deviceName 网络接口名称
     * @param port 要捕获的端口号
     */
    void startCapture(String deviceName, int port);

    /**
     * 停止数据包捕获
     */
    void stopCapture();

    /**
     * 检查数据包捕获是否正在运行
     *
     * @return 如果捕获正在运行则返回true
     */
    boolean isCaptureRunning();

    /**
     * 获取当前捕获配置
     *
     * @return 捕获配置，如果未运行则返回null
     */
    CaptureConfig getCurrentConfig();

    /**
     * 网络接口信息
     */
    interface NetworkInterface {
        String getName();
        String getDescription();
        List<String> getAddresses();
        boolean isUp();
        boolean isLoopback();
    }

    /**
     * 捕获配置
     */
    interface CaptureConfig {
        String getDeviceName();
        int getPort();
        long getStartTime();
        long getCapturedPackets();
    }
}
