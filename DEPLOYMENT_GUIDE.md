# Network Packet Capture Agent - JAR Deployment Guide

This guide provides comprehensive instructions for deploying the Network Packet Capture Agent as a standalone JAR application.

## Prerequisites

### System Requirements
- **Java**: OpenJDK 8 or higher
- **Memory**: Minimum 512MB RAM, recommended 1GB+
- **Storage**: 100MB for application, additional space for logs
- **Network**: Access to RocketMQ server
- **Permissions**: Root or sudo access for packet capture

### Required Software
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install openjdk-8-jdk maven libpcap-dev

# CentOS/RHEL
sudo yum install java-1.8.0-openjdk maven libpcap-devel

# Or download from Oracle/OpenJDK websites
```

### Network Permissions
The application requires elevated privileges for packet capture:
```bash
# Option 1: Run with sudo (simple but less secure)
sudo ./start.sh

# Option 2: Set capabilities (recommended for production)
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/java

# Option 3: Add user to specific groups (distribution-dependent)
sudo usermod -a -G wireshark $USER
```

## Quick Start

### 1. Download and Build
```bash
# Clone repository
git clone <repository-url>
cd net-pcap-agent

# Build application
mvn clean package -DskipTests

# Verify JAR file
ls -la target/net-pcap-agent-*.jar
```

### 2. Basic Configuration
```bash
# Set environment variables
export HOST_IP=*************
export ROCKETMQ_NAME_SERVER=*************:9876

# Or create application-local.yml
cp src/main/resources/application.yml application-local.yml
# Edit application-local.yml with your settings
```

### 3. Start Application
```bash
# Start with default settings
./start.sh --build

# Start with custom configuration
./start.sh --host-ip ************* --rocketmq-server *************:9876

# Start in background
./start.sh --background --profile prod
```

### 4. Verify Installation
```bash
# Check status
./status.sh

# Check health endpoint
curl http://localhost:25900/agent/net-pcap/actuator/health

# Access web dashboard
# Open browser: http://localhost:25900/agent/net-pcap/dashboard
```

## Production Deployment

### 1. Create Deployment Directory
```bash
# Create application directory
sudo mkdir -p /opt/net-pcap-agent
sudo mkdir -p /opt/net-pcap-agent/logs
sudo mkdir -p /opt/net-pcap-agent/config

# Create dedicated user
sudo useradd -r -s /bin/false -d /opt/net-pcap-agent pcap-agent

# Set permissions
sudo chown -R pcap-agent:pcap-agent /opt/net-pcap-agent
```

### 2. Deploy Application Files
```bash
# Copy JAR file
sudo cp target/net-pcap-agent-*.jar /opt/net-pcap-agent/

# Copy scripts
sudo cp start.sh stop.sh status.sh /opt/net-pcap-agent/
sudo chmod +x /opt/net-pcap-agent/*.sh

# Copy configuration
sudo cp src/main/resources/application-prod.yml /opt/net-pcap-agent/config/
```

### 3. Create Production Configuration
```bash
# Create production configuration file
sudo tee /opt/net-pcap-agent/config/application-prod.yml > /dev/null <<EOF
# Production configuration
spring:
  profiles:
    active: prod

packet-capture:
  host-ip: ${HOST_IP:*************}
  default-port: ${DEFAULT_PORT:80}
  
  buffer:
    max-size: 50000
    cleanup-interval: 120
    max-age: 600
  
  performance:
    worker-threads: 8
    pcap-buffer-size: 131072

rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: net-pcap-prod
    send-message-timeout: 5000

logging:
  file:
    name: /opt/net-pcap-agent/logs/net-pcap-agent.log
    max-size: 500MB
    max-history: 30
EOF
```

### 4. Create Systemd Service
```bash
# Create service file
sudo tee /etc/systemd/system/net-pcap-agent.service > /dev/null <<EOF
[Unit]
Description=Network Packet Capture Agent
Documentation=https://github.com/your-org/net-pcap-agent
After=network.target
Wants=network.target

[Service]
Type=forking
User=pcap-agent
Group=pcap-agent
WorkingDirectory=/opt/net-pcap-agent

# Environment variables
Environment=HOST_IP=*************
Environment=ROCKETMQ_NAME_SERVER=*************:9876
Environment=JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

# Start command
ExecStart=/opt/net-pcap-agent/start.sh --background --profile prod --host-ip \${HOST_IP} --rocketmq-server \${ROCKETMQ_NAME_SERVER}
ExecStop=/opt/net-pcap-agent/stop.sh
ExecReload=/bin/kill -HUP \$MAINPID

# Process management
PIDFile=/opt/net-pcap-agent/net-pcap-agent.pid
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/net-pcap-agent

# Capabilities for packet capture
AmbientCapabilities=CAP_NET_RAW CAP_NET_ADMIN
CapabilityBoundingSet=CAP_NET_RAW CAP_NET_ADMIN

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable net-pcap-agent
```

### 5. Start and Verify Service
```bash
# Start service
sudo systemctl start net-pcap-agent

# Check status
sudo systemctl status net-pcap-agent

# View logs
sudo journalctl -u net-pcap-agent -f

# Check application status
sudo -u pcap-agent /opt/net-pcap-agent/status.sh --detailed
```

## Configuration Management

### Environment Variables
```bash
# Core settings
export HOST_IP=*************
export ROCKETMQ_NAME_SERVER=*************:9876
export DEFAULT_PORT=8080
export WORKER_THREADS=8

# Performance tuning
export BUFFER_MAX_SIZE=50000
export PCAP_BUFFER_SIZE=131072
export REASSEMBLY_CONFIRM_DURATION=10

# JVM tuning
export JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"
```

### Profile-Specific Configuration
```bash
# Development
./start.sh --dev --build

# Production
./start.sh --profile prod --background

# Custom profile
./start.sh --profile custom --spring.config.location=config/application-custom.yml
```

## Monitoring and Maintenance

### Health Monitoring
```bash
# Basic health check
curl -f http://localhost:25900/agent/net-pcap/actuator/health

# Detailed health with authentication
curl -u admin:password http://localhost:25900/agent/net-pcap/actuator/health

# Metrics endpoint
curl http://localhost:25900/agent/net-pcap/actuator/metrics
```

### Log Management
```bash
# View real-time logs
tail -f /opt/net-pcap-agent/logs/net-pcap-agent.log

# Search for errors
grep ERROR /opt/net-pcap-agent/logs/net-pcap-agent.log

# Log rotation (automatic with configuration)
# Manual rotation if needed
sudo logrotate -f /etc/logrotate.d/net-pcap-agent
```

### Performance Monitoring
```bash
# Check process resources
./status.sh --detailed

# Monitor network connections
sudo netstat -tulpn | grep java

# Check packet capture statistics
curl http://localhost:25900/agent/net-pcap/api/v1/stats
```

## Troubleshooting

### Common Issues

1. **Permission Denied for Packet Capture**
   ```bash
   # Check capabilities
   getcap /usr/bin/java
   
   # Set capabilities
   sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/java
   ```

2. **RocketMQ Connection Failed**
   ```bash
   # Test connectivity
   telnet ************* 9876
   
   # Check firewall
   sudo ufw status
   sudo firewall-cmd --list-all
   ```

3. **High Memory Usage**
   ```bash
   # Adjust JVM settings
   ./start.sh --jvm-opts "-Xmx1g -XX:+UseG1GC"
   
   # Monitor memory
   jstat -gc $(cat net-pcap-agent.pid)
   ```

4. **Application Won't Start**
   ```bash
   # Check Java version
   java -version
   
   # Check JAR file
   java -jar target/net-pcap-agent-*.jar --help
   
   # Check logs
   tail -f logs/net-pcap-agent.log
   ```

### Performance Tuning

1. **High Traffic Environments**
   ```bash
   # Increase worker threads and buffer sizes
   ./start.sh --worker-threads 16 --jvm-opts "-Xmx4g"
   ```

2. **Memory Optimization**
   ```bash
   # Tune garbage collection
   ./start.sh --jvm-opts "-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
   ```

3. **Network Optimization**
   ```yaml
   # In application.yml
   packet-capture:
     performance:
       pcap-buffer-size: 262144  # Larger buffer
       pcap-timeout: 500         # Shorter timeout
   ```

## Security Considerations

### Network Security
- Run on isolated network segment if possible
- Use firewall rules to restrict access
- Enable HTTPS for web dashboard in production

### Application Security
- Run with minimal required privileges
- Use dedicated service account
- Enable authentication for actuator endpoints
- Regularly update dependencies

### Data Security
- Encrypt sensitive configuration data
- Secure log files with appropriate permissions
- Consider data retention policies for captured packets

This deployment guide provides a comprehensive approach to deploying the Network Packet Capture Agent in production environments using JAR-based deployment.
