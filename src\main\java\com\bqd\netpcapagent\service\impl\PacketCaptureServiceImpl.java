package com.bqd.netpcapagent.service.impl;

import com.bqd.netpcapagent.config.PacketCaptureProperties;
import com.bqd.netpcapagent.exception.PacketCaptureException;
import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.pcap4j.core.*;
import org.pcap4j.packet.Packet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.net.Inet4Address;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implementation of packet capture service
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Slf4j
@Service
public class PacketCaptureServiceImpl implements PacketCaptureService {

    @Autowired
    private PacketCaptureProperties properties;

    @Autowired
    private PacketProcessingService packetProcessingService;

    private volatile PcapHandle pcapHandle;
    private volatile Thread captureThread;
    private final AtomicBoolean captureRunning = new AtomicBoolean(false);
    private volatile CaptureConfigImpl currentConfig;
    private final AtomicLong capturedPackets = new AtomicLong(0);

    @Override
    public List<NetworkInterface> getNetworkInterfaces() {
        try {
            List<PcapNetworkInterface> pcapInterfaces = Pcaps.findAllDevs();
            return pcapInterfaces.stream()
                    .map(this::convertToNetworkInterface)
                    .collect(Collectors.toList());
        } catch (PcapNativeException e) {
            throw new PacketCaptureException("Failed to retrieve network interfaces", e);
        }
    }

    private NetworkInterface convertToNetworkInterface(PcapNetworkInterface pcapInterface) {
        return new NetworkInterfaceImpl(
                pcapInterface.getName(),
                pcapInterface.getDescription(),
                pcapInterface.getAddresses().stream()
                        .filter(addr -> addr.getAddress() instanceof Inet4Address)
                        .map(addr -> addr.getAddress().getHostAddress())
                        .collect(Collectors.toList()),
                pcapInterface.isUp(),
                pcapInterface.isLoopBack()
        );
    }

    @Override
    public void startCapture(String deviceName, int port) {
        if (captureRunning.get()) {
            throw new PacketCaptureException("Packet capture is already running");
        }

        try {
            // Validate device
            PcapNetworkInterface pcapNetworkInterface = Pcaps.getDevByName(deviceName);
            if (pcapNetworkInterface == null) {
                throw new PacketCaptureException("Network device not found: " + deviceName);
            }

            // Open capture handle
            pcapHandle = pcapNetworkInterface.openLive(
                    properties.getPerformance().getPcapBufferSize(),
                    PcapNetworkInterface.PromiscuousMode.PROMISCUOUS,
                    properties.getPerformance().getPcapTimeout()
            );

            // Set filter
            String filter = "tcp port " + port;
            pcapHandle.setFilter(filter, BpfProgram.BpfCompileMode.OPTIMIZE);

            // Create capture configuration
            currentConfig = new CaptureConfigImpl(deviceName, port, System.currentTimeMillis());
            capturedPackets.set(0);

            // Start capture thread
            captureRunning.set(true);
            captureThread = new Thread(this::runCapture, "packet-capture");
            captureThread.setDaemon(true);
            captureThread.start();

            log.info("Packet capture started on device {} port {} with filter: {}", 
                    deviceName, port, filter);

        } catch (PcapNativeException | NotOpenException e) {
            throw new PacketCaptureException("Failed to start packet capture", e);
        }
    }

    private void runCapture() {
        log.info("Starting packet capture loop");
        
        try {
            while (captureRunning.get()) {
                try {
                    Packet packet = pcapHandle.getNextPacket();
                    if (packet != null) {
                        capturedPackets.incrementAndGet();
                        packetProcessingService.processPacket(packet);
                    }
                } catch (Exception e) {
                    if (captureRunning.get()) {
                        log.error("Error capturing packet", e);
                    }
                }
            }
        } finally {
            log.info("Packet capture loop ended");
        }
    }

    @Override
    public void stopCapture() {
        if (!captureRunning.get()) {
            log.warn("Packet capture is not running");
            return;
        }

        captureRunning.set(false);

        // Close pcap handle
        if (pcapHandle != null) {
            try {
                pcapHandle.close();
            } catch (Exception e) {
                log.warn("Error closing pcap handle", e);
            }
            pcapHandle = null;
        }

        // Wait for capture thread to finish
        if (captureThread != null) {
            try {
                captureThread.join(5000); // Wait up to 5 seconds
                if (captureThread.isAlive()) {
                    log.warn("Capture thread did not stop gracefully");
                    captureThread.interrupt();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for capture thread to stop");
            }
            captureThread = null;
        }

        currentConfig = null;
        log.info("Packet capture stopped");
    }

    @Override
    public boolean isCaptureRunning() {
        return captureRunning.get();
    }

    @Override
    public CaptureConfig getCurrentConfig() {
        CaptureConfigImpl config = currentConfig;
        if (config != null) {
            config.setCapturedPackets(capturedPackets.get());
        }
        return config;
    }

    @PreDestroy
    public void destroy() {
        stopCapture();
    }

    // Implementation classes
    private static class NetworkInterfaceImpl implements NetworkInterface {
        private final String name;
        private final String description;
        private final List<String> addresses;
        private final boolean up;
        private final boolean loopback;

        public NetworkInterfaceImpl(String name, String description, List<String> addresses, 
                                   boolean up, boolean loopback) {
            this.name = name;
            this.description = description != null ? description : "";
            this.addresses = new ArrayList<>(addresses);
            this.up = up;
            this.loopback = loopback;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getDescription() {
            return description;
        }

        @Override
        public List<String> getAddresses() {
            return new ArrayList<>(addresses);
        }

        @Override
        public boolean isUp() {
            return up;
        }

        @Override
        public boolean isLoopback() {
            return loopback;
        }
    }

    private static class CaptureConfigImpl implements CaptureConfig {
        private final String deviceName;
        private final int port;
        private final long startTime;
        private volatile long capturedPackets;

        public CaptureConfigImpl(String deviceName, int port, long startTime) {
            this.deviceName = deviceName;
            this.port = port;
            this.startTime = startTime;
        }

        @Override
        public String getDeviceName() {
            return deviceName;
        }

        @Override
        public int getPort() {
            return port;
        }

        @Override
        public long getStartTime() {
            return startTime;
        }

        @Override
        public long getCapturedPackets() {
            return capturedPackets;
        }

        public void setCapturedPackets(long capturedPackets) {
            this.capturedPackets = capturedPackets;
        }
    }
}
