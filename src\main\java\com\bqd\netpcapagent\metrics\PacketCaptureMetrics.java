package com.bqd.netpcapagent.metrics;

import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 数据包捕获功能的指标组件
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Component
public class PacketCaptureMetrics {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @PostConstruct
    public void initMetrics() {
        // 捕获状态指标
        Gauge.builder("packet_capture.running", packetCaptureService, 
                service -> service.isCaptureRunning() ? 1.0 : 0.0)
                .description("数据包捕获是否正在运行")
                .register(meterRegistry);

        Gauge.builder("packet_reassembly.running", packetProcessingService,
                service -> service.isReassemblyRunning() ? 1.0 : 0.0)
                .description("数据包重组是否正在运行")
                .register(meterRegistry);

        // 处理统计指标
        Gauge.builder("packet_processing.processed_packets", packetProcessingService,
                service -> (double) service.getStats().getProcessedPackets())
                .description("已处理数据包总数")
                .register(meterRegistry);

        Gauge.builder("packet_processing.reassembled_sessions", packetProcessingService,
                service -> (double) service.getStats().getReassembledSessions())
                .description("已重组会话总数")
                .register(meterRegistry);

        Gauge.builder("packet_processing.sent_messages", packetProcessingService,
                service -> (double) service.getStats().getSentMessages())
                .description("发送到RocketMQ的消息总数")
                .register(meterRegistry);

        Gauge.builder("packet_processing.error_count", packetProcessingService,
                service -> (double) service.getStats().getErrorCount())
                .description("处理错误总数")
                .register(meterRegistry);

        Gauge.builder("packet_processing.current_buffer_size", packetProcessingService,
                service -> (double) service.getStats().getCurrentBufferSize())
                .description("缓冲区中当前数据包数量")
                .register(meterRegistry);

        Gauge.builder("packet_processing.dropped_packets", packetProcessingService,
                service -> (double) service.getStats().getDroppedPackets())
                .description("丢弃数据包总数")
                .register(meterRegistry);

        // 捕获配置指标
        Gauge.builder("packet_capture.current_port", packetCaptureService, service -> {
                    PacketCaptureService.CaptureConfig config = service.getCurrentConfig();
                    return config != null ? (double) config.getPort() : 0.0;
                })
                .description("当前配置的捕获端口")
                .register(meterRegistry);

        Gauge.builder("packet_capture.captured_packets", packetCaptureService, service -> {
                    PacketCaptureService.CaptureConfig config = service.getCurrentConfig();
                    return config != null ? (double) config.getCapturedPackets() : 0.0;
                })
                .description("当前会话中捕获的数据包数量")
                .register(meterRegistry);

        // 派生指标
        Gauge.builder("packet_processing.error_rate", packetProcessingService, service -> {
                    PacketProcessingService.ProcessingStats stats = service.getStats();
                    long processed = stats.getProcessedPackets();
                    long errors = stats.getErrorCount();
                    return processed > 0 ? (double) errors / processed * 100.0 : 0.0;
                })
                .description("错误率（已处理数据包的百分比）")
                .register(meterRegistry);

        Gauge.builder("packet_processing.drop_rate", packetProcessingService, service -> {
                    PacketProcessingService.ProcessingStats stats = service.getStats();
                    long processed = stats.getProcessedPackets();
                    long dropped = stats.getDroppedPackets();
                    long total = processed + dropped;
                    return total > 0 ? (double) dropped / total * 100.0 : 0.0;
                })
                .description("丢包率（总数据包的百分比）")
                .register(meterRegistry);

        Gauge.builder("packet_processing.reassembly_rate", packetProcessingService, service -> {
                    PacketProcessingService.ProcessingStats stats = service.getStats();
                    long processed = stats.getProcessedPackets();
                    long reassembled = stats.getReassembledSessions() * 2; // 每个会话代表2个数据包（请求+响应）
                    return processed > 0 ? (double) reassembled / processed * 100.0 : 0.0;
                })
                .description("重组率（已处理数据包的百分比）")
                .register(meterRegistry);
    }
}

