package com.bqd.netpcapagent.controller;

import com.bqd.netpcapagent.config.PacketCaptureProperties;
import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 数据包捕获代理的Web控制面板控制器
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Controller
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @Autowired
    private PacketCaptureProperties properties;

    /**
     * 主控制面板页面
     */
    @GetMapping({"", "/"})
    public String dashboard(Model model) {
        model.addAttribute("interfaces", packetCaptureService.getNetworkInterfaces());
        model.addAttribute("captureRunning", packetCaptureService.isCaptureRunning());
        model.addAttribute("reassemblyRunning", packetProcessingService.isReassemblyRunning());
        model.addAttribute("currentConfig", packetCaptureService.getCurrentConfig());
        model.addAttribute("stats", packetProcessingService.getStats());
        model.addAttribute("properties", properties);

        return "dashboard";
    }

    /**
     * 配置页面
     */
    @GetMapping("/config")
    public String configuration(Model model) {
        model.addAttribute("properties", properties);
        model.addAttribute("interfaces", packetCaptureService.getNetworkInterfaces());

        return "config";
    }

    /**
     * 监控页面
     */
    @GetMapping("/monitor")
    public String monitoring(Model model) {
        model.addAttribute("stats", packetProcessingService.getStats());
        model.addAttribute("requestSessions", packetProcessingService.getRequestSessions());
        model.addAttribute("responseSessions", packetProcessingService.getResponseSessions());

        return "monitor";
    }
}
