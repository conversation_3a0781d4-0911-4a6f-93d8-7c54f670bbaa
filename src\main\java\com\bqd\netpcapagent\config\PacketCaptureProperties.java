package com.bqd.netpcapagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据包捕获功能的配置属性
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@Component
@ConfigurationProperties(prefix = "packet-capture")
@Validated
public class PacketCaptureProperties {

    /**
     * 用于区分请求/响应数据包的主机IP
     */
    @NotBlank(message = "主机IP不能为空")
    private String hostIp = "127.0.0.1";

    /**
     * 默认网络接口
     */
    private String defaultInterface;

    /**
     * 默认捕获端口
     */
    @Min(value = 1, message = "端口必须大于0")
    private int defaultPort = 80;

    /**
     * 数据包缓冲区配置
     */
    @Valid
    @NotNull
    private Buffer buffer = new Buffer();

    /**
     * HTTP重组配置
     */
    @Valid
    @NotNull
    private Reassembly reassembly = new Reassembly();

    /**
     * 性能调优配置
     */
    @Valid
    @NotNull
    private Performance performance = new Performance();

    @Data
    public static class Buffer {
        /**
         * 处理前缓冲的最大数据包数量
         */
        @Min(value = 100, message = "缓冲区最大大小必须至少为100")
        private int maxSize = 10000;

        /**
         * 缓冲区清理间隔（秒）
         */
        @Min(value = 10, message = "清理间隔必须至少为10秒")
        private int cleanupInterval = 60;

        /**
         * 缓冲区中数据包清理前的最大存活时间（秒）
         */
        @Min(value = 30, message = "最大存活时间必须至少为30秒")
        private int maxAge = 300;
    }

    @Data
    public static class Reassembly {
        /**
         * 默认确认持续时间（秒）
         */
        @Min(value = 1, message = "确认持续时间必须至少为1秒")
        private long defaultConfirmDuration = 5;

        /**
         * 等待数据包重组的最大时间（秒）
         */
        @Min(value = 5, message = "最大等待时间必须至少为5秒")
        private int maxWaitTime = 30;

        /**
         * 处理数据包的批次大小
         */
        @Min(value = 1, message = "批次大小必须至少为1")
        private int batchSize = 100;
    }

    @Data
    public static class Performance {
        /**
         * 数据包处理的工作线程数
         */
        @Min(value = 1, message = "工作线程数必须至少为1")
        private int workerThreads = 4;

        /**
         * Pcap缓冲区大小
         */
        @Min(value = 1024, message = "Pcap缓冲区大小必须至少为1024")
        private int pcapBufferSize = 65536;

        /**
         * Pcap超时时间（毫秒）
         */
        @Min(value = 100, message = "Pcap超时时间必须至少为100毫秒")
        private int pcapTimeout = 1000;
    }
}
