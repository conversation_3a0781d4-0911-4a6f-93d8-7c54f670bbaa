package com.bqd.netpcapagent.service;

import com.bqd.netpcapagent.model.TcpSession;
import org.pcap4j.packet.Packet;

import java.util.List;

/**
 * 数据包处理操作的服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
public interface PacketProcessingService {

    /**
     * 处理捕获的数据包
     *
     * @param packet 捕获的数据包
     */
    void processPacket(Packet packet);

    /**
     * 启动数据包重组和发送进程
     */
    void startReassemblyProcess();

    /**
     * 停止数据包重组和发送进程
     */
    void stopReassemblyProcess();

    /**
     * 获取当前请求数据包会话
     *
     * @return 请求会话列表
     */
    List<TcpSession> getRequestSessions();

    /**
     * 获取当前响应数据包会话
     *
     * @return 响应会话列表
     */
    List<TcpSession> getResponseSessions();

    /**
     * 清空所有数据包缓冲区
     */
    void clearBuffers();

    /**
     * 获取处理统计信息
     *
     * @return 处理统计信息
     */
    ProcessingStats getStats();

    /**
     * 检查重组进程是否正在运行
     *
     * @return 如果正在运行则返回true
     */
    boolean isReassemblyRunning();

    /**
     * 处理统计信息
     */
    interface ProcessingStats {
        long getProcessedPackets();
        long getReassembledSessions();
        long getSentMessages();
        long getErrorCount();
        int getCurrentBufferSize();
        long getDroppedPackets();
    }
}
