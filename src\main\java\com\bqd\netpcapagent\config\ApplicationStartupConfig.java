package com.bqd.netpcapagent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 应用程序启动配置和验证
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Slf4j
@Component
public class ApplicationStartupConfig {

    @Autowired
    private PacketCaptureProperties properties;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== 网络数据包捕获代理已启动 ===");
        log.info("部署模式: 基于JAR的独立部署");
        log.info("主机IP: {}", properties.getHostIp());
        log.info("默认网络接口: {}", properties.getDefaultInterface());
        log.info("默认端口: {}", properties.getDefaultPort());
        log.info("工作线程数: {}", properties.getPerformance().getWorkerThreads());
        log.info("缓冲区最大大小: {}", properties.getBuffer().getMaxSize());
        log.info("Pcap缓冲区大小: {}", properties.getPerformance().getPcapBufferSize());
        log.info("");
        log.info("访问地址:");
        log.info("  控制面板: http://localhost:25900/agent/net-pcap/dashboard");
        log.info("  REST API: http://localhost:25900/agent/net-pcap/api/v1");
        log.info("  健康检查: http://localhost:25900/agent/net-pcap/actuator/health");
        log.info("  指标监控: http://localhost:25900/agent/net-pcap/actuator/metrics");
        log.info("");
        log.info("使用Web控制面板启动数据包捕获或使用REST API");
        log.info("===========================================");
    }
}
