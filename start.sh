#!/bin/bash

# Network Packet Capture Agent Startup Script
# This script provides an easy way to start the packet capture agent with proper configuration

set -e

# Default configuration
DEFAULT_HOST_IP="127.0.0.1"
DEFAULT_PORT="80"
DEFAULT_ROCKETMQ_SERVER="localhost:9876"
DEFAULT_WORKER_THREADS="4"
DEFAULT_PROFILE="prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help                    Show this help message"
    echo "  --host-ip IP                  Host IP address (default: $DEFAULT_HOST_IP)"
    echo "  --port PORT                   Default capture port (default: $DEFAULT_PORT)"
    echo "  --rocketmq-server SERVER      RocketMQ server address (default: $DEFAULT_ROCKETMQ_SERVER)"
    echo "  --worker-threads THREADS      Number of worker threads (default: $DEFAULT_WORKER_THREADS)"
    echo "  --profile PROFILE             Spring profile to use (default: $DEFAULT_PROFILE)"
    echo "  --build                       Build the application before starting"
    echo "  --dev                         Start in development mode with debug logging"
    echo "  --background                  Start in background mode"
    echo "  --jvm-opts OPTS               Additional JVM options"
    echo ""
    echo "Examples:"
    echo "  $0 --host-ip ************* --port 8080"
    echo "  $0 --build --dev"
    echo "  $0 --profile prod --background"
    echo "  $0 --jvm-opts \"-Xmx1g -XX:+UseG1GC\""
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."

    # Check Java
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi

    local java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    print_info "Java version: $java_version"

    # Check Maven if building
    if [[ "$BUILD" == "true" ]]; then
        if ! command -v mvn &> /dev/null; then
            print_error "Maven is not installed or not in PATH"
            exit 1
        fi
        print_info "Maven found: $(mvn -version | head -n 1)"
    fi

    # Check if RocketMQ is accessible (optional check)
    if command -v nc &> /dev/null; then
        local rocketmq_host=$(echo $ROCKETMQ_SERVER | cut -d: -f1)
        local rocketmq_port=$(echo $ROCKETMQ_SERVER | cut -d: -f2)
        if nc -z "$rocketmq_host" "$rocketmq_port" 2>/dev/null; then
            print_info "RocketMQ server is accessible at $ROCKETMQ_SERVER"
        else
            print_warning "RocketMQ server at $ROCKETMQ_SERVER is not accessible (this is OK if starting RocketMQ separately)"
        fi
    fi

    print_success "Prerequisites check completed"
}

# Function to build the application
build_application() {
    print_info "Building the application..."

    if [[ ! -f "pom.xml" ]]; then
        print_error "pom.xml not found. Please run this script from the project root directory."
        exit 1
    fi

    mvn clean package -DskipTests

    if [[ $? -eq 0 ]]; then
        print_success "Application built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to create PID file for background mode
create_pid_file() {
    local pid_file="net-pcap-agent.pid"
    echo $! > "$pid_file"
    print_info "PID file created: $pid_file"
}

# Function to check if application is already running
check_running() {
    local pid_file="net-pcap-agent.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            print_warning "Application is already running with PID $pid"
            print_info "To stop: kill $pid && rm $pid_file"
            return 0
        else
            print_info "Removing stale PID file"
            rm -f "$pid_file"
        fi
    fi
    return 1
}

# Function to start standalone
start_standalone() {
    print_info "Starting packet capture agent..."

    # Check if already running
    if check_running; then
        exit 1
    fi

    # Find the JAR file
    JAR_FILE=$(find target -name "net-pcap-agent-*.jar" | head -n 1)

    if [[ -z "$JAR_FILE" ]]; then
        print_error "JAR file not found. Please build the application first with --build option."
        exit 1
    fi

    print_info "Using JAR file: $JAR_FILE"

    # Create logs directory
    mkdir -p logs

    # Set JVM options
    JVM_OPTS="-Xmx512m -Xms256m -XX:+UseG1GC"

    # Add custom JVM options if provided
    if [[ -n "$CUSTOM_JVM_OPTS" ]]; then
        JVM_OPTS="$JVM_OPTS $CUSTOM_JVM_OPTS"
    fi

    # Set Spring profile
    if [[ "$DEV_MODE" == "true" ]]; then
        PROFILE="dev"
        print_info "Starting in development mode with debug logging"
    fi

    # Set application properties
    APP_PROPS=""
    APP_PROPS="$APP_PROPS --spring.profiles.active=$PROFILE"
    APP_PROPS="$APP_PROPS --packet-capture.host-ip=$HOST_IP"
    APP_PROPS="$APP_PROPS --packet-capture.default-port=$PORT"
    APP_PROPS="$APP_PROPS --rocketmq.name-server=$ROCKETMQ_SERVER"
    APP_PROPS="$APP_PROPS --packet-capture.performance.worker-threads=$WORKER_THREADS"

    print_info "Configuration:"
    print_info "  Profile: $PROFILE"
    print_info "  Host IP: $HOST_IP"
    print_info "  Default Port: $PORT"
    print_info "  RocketMQ Server: $ROCKETMQ_SERVER"
    print_info "  Worker Threads: $WORKER_THREADS"
    print_info "  JVM Options: $JVM_OPTS"
    print_info ""

    # Start the application
    print_info "Starting application..."
    print_info "Dashboard will be available at: http://localhost:25900/agent/net-pcap/dashboard"
    print_info "API will be available at: http://localhost:25900/agent/net-pcap/api/v1"
    print_info ""

    if [[ "$BACKGROUND_MODE" == "true" ]]; then
        print_info "Starting in background mode..."
        nohup java $JVM_OPTS -jar "$JAR_FILE" $APP_PROPS > logs/application.log 2>&1 &
        create_pid_file
        print_success "Application started in background"
        print_info "View logs: tail -f logs/application.log"
    else
        java $JVM_OPTS -jar "$JAR_FILE" $APP_PROPS
    fi
}

# Parse command line arguments
HOST_IP="$DEFAULT_HOST_IP"
PORT="$DEFAULT_PORT"
ROCKETMQ_SERVER="$DEFAULT_ROCKETMQ_SERVER"
WORKER_THREADS="$DEFAULT_WORKER_THREADS"
PROFILE="$DEFAULT_PROFILE"
BUILD="false"
DEV_MODE="false"
BACKGROUND_MODE="false"
CUSTOM_JVM_OPTS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --host-ip)
            HOST_IP="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --rocketmq-server)
            ROCKETMQ_SERVER="$2"
            shift 2
            ;;
        --worker-threads)
            WORKER_THREADS="$2"
            shift 2
            ;;
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --jvm-opts)
            CUSTOM_JVM_OPTS="$2"
            shift 2
            ;;
        --build)
            BUILD="true"
            shift
            ;;
        --dev)
            DEV_MODE="true"
            shift
            ;;
        --background)
            BACKGROUND_MODE="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
print_info "Network Packet Capture Agent Startup Script"
print_info "============================================"

# Check prerequisites
check_prerequisites

# Build if requested
if [[ "$BUILD" == "true" ]]; then
    build_application
fi

# Start the application
start_standalone
