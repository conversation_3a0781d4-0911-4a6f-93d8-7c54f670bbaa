package com.bqd.netpcapagent.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pcap4j.packet.TcpPacket;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 表示包含相关数据包的TCP会话
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcpSession {

    /**
     * 此会话中的TCP数据包列表
     */
    private List<TcpPacket> tcpPacketList = new ArrayList<>();

    /**
     * 会话创建时间
     */
    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime = LocalDateTime.now();

    /**
     * 会话标识符（确认号）
     */
    private Long sessionId;

    /**
     * 此会话是否完成
     */
    private boolean complete = false;

    /**
     * 会话类型（请求或响应）
     */
    private SessionType type;

    /**
     * 源IP地址
     */
    private String sourceIp;

    /**
     * 目标IP地址
     */
    private String destinationIp;

    /**
     * 源端口
     */
    private int sourcePort;

    /**
     * 目标端口
     */
    private int destinationPort;

    public enum SessionType {
        REQUEST, RESPONSE
    }

    /**
     * 向此会话添加数据包
     *
     * @param packet 要添加的数据包
     */
    public void addPacket(TcpPacket packet) {
        this.tcpPacketList.add(packet);
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 获取此会话中所有数据包的总负载大小
     *
     * @return 总负载大小（字节）
     */
    public int getTotalPayloadSize() {
        return tcpPacketList.stream()
                .filter(packet -> packet.getPayload() != null)
                .mapToInt(packet -> packet.getPayload().getRawData().length)
                .sum();
    }

    /**
     * 根据给定的超时时间检查此会话是否已过期
     *
     * @param timeoutSeconds 超时时间（秒）
     * @return 如果已过期则返回true
     */
    public boolean isExpired(int timeoutSeconds) {
        return lastUpdateTime.isBefore(LocalDateTime.now().minusSeconds(timeoutSeconds));
    }
}
