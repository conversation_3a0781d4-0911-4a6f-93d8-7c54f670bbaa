#!/bin/bash

# Network Packet Capture Agent Status Script
# This script checks the status of the packet capture agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help        Show this help message"
    echo "  --pid-file FILE   Specify custom PID file location (default: net-pcap-agent.pid)"
    echo "  --detailed        Show detailed status information"
    echo "  --health          Check application health endpoint"
    echo ""
    echo "Examples:"
    echo "  $0                # Basic status check"
    echo "  $0 --detailed     # Detailed status with process info"
    echo "  $0 --health       # Check health endpoint"
}

# Function to check process status
check_process_status() {
    local pid_file="$1"
    
    if [[ ! -f "$pid_file" ]]; then
        print_warning "PID file not found: $pid_file"
        print_info "Application is not running (or not started with background mode)"
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    
    if [[ -z "$pid" ]]; then
        print_error "PID file is empty: $pid_file"
        return 1
    fi
    
    if ps -p "$pid" > /dev/null 2>&1; then
        print_success "Application is running (PID: $pid)"
        return 0
    else
        print_error "Process with PID $pid is not running"
        print_warning "Stale PID file found: $pid_file"
        return 1
    fi
}

# Function to show detailed process information
show_detailed_status() {
    local pid_file="$1"
    
    if [[ ! -f "$pid_file" ]]; then
        print_warning "Cannot show detailed status - PID file not found"
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    
    if [[ -z "$pid" ]] || ! ps -p "$pid" > /dev/null 2>&1; then
        print_warning "Cannot show detailed status - process not running"
        return 1
    fi
    
    print_info "Detailed Process Information:"
    echo "----------------------------------------"
    
    # Process details
    if command -v ps &> /dev/null; then
        echo "Process Details:"
        ps -p "$pid" -o pid,ppid,user,start,time,command 2>/dev/null || echo "  Unable to get process details"
        echo ""
    fi
    
    # Memory usage
    if [[ -f "/proc/$pid/status" ]]; then
        echo "Memory Usage:"
        grep -E "VmSize|VmRSS|VmPeak" "/proc/$pid/status" 2>/dev/null | sed 's/^/  /' || echo "  Unable to get memory info"
        echo ""
    fi
    
    # Open files
    if command -v lsof &> /dev/null; then
        echo "Network Connections:"
        lsof -p "$pid" -i 2>/dev/null | head -10 | sed 's/^/  /' || echo "  Unable to get network connections"
        echo ""
    fi
    
    # Log file info
    if [[ -f "logs/net-pcap-agent.log" ]]; then
        echo "Log File Info:"
        echo "  Location: logs/net-pcap-agent.log"
        echo "  Size: $(du -h logs/net-pcap-agent.log 2>/dev/null | cut -f1 || echo "Unknown")"
        echo "  Last Modified: $(stat -c %y logs/net-pcap-agent.log 2>/dev/null || echo "Unknown")"
        echo ""
    fi
    
    if [[ -f "logs/application.log" ]]; then
        echo "Application Log Info:"
        echo "  Location: logs/application.log"
        echo "  Size: $(du -h logs/application.log 2>/dev/null | cut -f1 || echo "Unknown")"
        echo "  Last Modified: $(stat -c %y logs/application.log 2>/dev/null || echo "Unknown")"
        echo ""
    fi
}

# Function to check health endpoint
check_health_endpoint() {
    local health_url="http://localhost:25900/agent/net-pcap/actuator/health"
    
    print_info "Checking health endpoint: $health_url"
    
    if command -v curl &> /dev/null; then
        local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$health_url" 2>/dev/null || echo "000")
        
        if [[ "$response" == "200" ]]; then
            print_success "Health endpoint is accessible"
            
            if command -v jq &> /dev/null; then
                echo "Health Status:"
                jq '.' /tmp/health_response.json 2>/dev/null | sed 's/^/  /' || cat /tmp/health_response.json | sed 's/^/  /'
            else
                echo "Health Response:"
                cat /tmp/health_response.json | sed 's/^/  /'
            fi
            
            rm -f /tmp/health_response.json
        else
            print_error "Health endpoint not accessible (HTTP $response)"
            print_info "Application may not be running or health endpoint may be disabled"
        fi
    else
        print_warning "curl not available - cannot check health endpoint"
        print_info "Install curl to enable health endpoint checking"
    fi
}

# Function to show application URLs
show_application_urls() {
    print_info "Application Access Points:"
    echo "  Dashboard:    http://localhost:25900/agent/net-pcap/dashboard"
    echo "  REST API:     http://localhost:25900/agent/net-pcap/api/v1"
    echo "  Health Check: http://localhost:25900/agent/net-pcap/actuator/health"
    echo "  Metrics:      http://localhost:25900/agent/net-pcap/actuator/metrics"
    echo ""
}

# Parse command line arguments
PID_FILE="net-pcap-agent.pid"
DETAILED="false"
CHECK_HEALTH="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --pid-file)
            PID_FILE="$2"
            shift 2
            ;;
        --detailed)
            DETAILED="true"
            shift
            ;;
        --health)
            CHECK_HEALTH="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
print_info "Network Packet Capture Agent Status Check"
print_info "=========================================="

# Check basic process status
if check_process_status "$PID_FILE"; then
    STATUS_OK=true
else
    STATUS_OK=false
fi

echo ""

# Show application URLs
show_application_urls

# Show detailed status if requested
if [[ "$DETAILED" == "true" ]] && [[ "$STATUS_OK" == "true" ]]; then
    show_detailed_status "$PID_FILE"
fi

# Check health endpoint if requested
if [[ "$CHECK_HEALTH" == "true" ]]; then
    check_health_endpoint
    echo ""
fi

# Final status
if [[ "$STATUS_OK" == "true" ]]; then
    print_success "Network Packet Capture Agent is running normally"
    exit 0
else
    print_error "Network Packet Capture Agent is not running"
    exit 1
fi
