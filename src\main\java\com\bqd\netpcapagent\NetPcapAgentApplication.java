package com.bqd.netpcapagent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * 网络数据包捕获代理的主应用程序类
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@SpringBootApplication
@EnableConfigurationProperties
public class NetPcapAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(NetPcapAgentApplication.class, args);
    }
}
