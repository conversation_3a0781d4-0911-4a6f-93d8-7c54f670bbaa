#!/bin/bash

# Network Packet Capture Agent Stop Script
# This script stops the packet capture agent running in background mode

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help        Show this help message"
    echo "  -f, --force       Force kill if graceful shutdown fails"
    echo "  --pid-file FILE   Specify custom PID file location (default: net-pcap-agent.pid)"
    echo ""
    echo "Examples:"
    echo "  $0                # Stop using default PID file"
    echo "  $0 --force        # Force kill if needed"
    echo "  $0 --pid-file /var/run/net-pcap-agent.pid"
}

# Function to stop the application
stop_application() {
    local pid_file="$1"
    local force_kill="$2"
    
    if [[ ! -f "$pid_file" ]]; then
        print_warning "PID file not found: $pid_file"
        print_info "Application may not be running or was started without background mode"
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    
    if [[ -z "$pid" ]]; then
        print_error "PID file is empty: $pid_file"
        rm -f "$pid_file"
        return 1
    fi
    
    # Check if process is running
    if ! ps -p "$pid" > /dev/null 2>&1; then
        print_warning "Process with PID $pid is not running"
        print_info "Removing stale PID file"
        rm -f "$pid_file"
        return 1
    fi
    
    print_info "Stopping Network Packet Capture Agent (PID: $pid)..."
    
    # Try graceful shutdown first
    if kill -TERM "$pid" 2>/dev/null; then
        print_info "Sent SIGTERM signal, waiting for graceful shutdown..."
        
        # Wait up to 30 seconds for graceful shutdown
        local count=0
        while ps -p "$pid" > /dev/null 2>&1 && [[ $count -lt 30 ]]; do
            sleep 1
            count=$((count + 1))
        done
        
        if ps -p "$pid" > /dev/null 2>&1; then
            if [[ "$force_kill" == "true" ]]; then
                print_warning "Graceful shutdown failed, force killing..."
                if kill -KILL "$pid" 2>/dev/null; then
                    print_info "Process force killed"
                else
                    print_error "Failed to force kill process"
                    return 1
                fi
            else
                print_error "Graceful shutdown failed. Use --force to force kill the process"
                print_info "Or manually kill with: kill -KILL $pid"
                return 1
            fi
        else
            print_success "Application stopped gracefully"
        fi
    else
        print_error "Failed to send SIGTERM signal to process $pid"
        return 1
    fi
    
    # Remove PID file
    if rm -f "$pid_file"; then
        print_info "PID file removed: $pid_file"
    else
        print_warning "Failed to remove PID file: $pid_file"
    fi
    
    return 0
}

# Parse command line arguments
PID_FILE="net-pcap-agent.pid"
FORCE_KILL="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -f|--force)
            FORCE_KILL="true"
            shift
            ;;
        --pid-file)
            PID_FILE="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
print_info "Network Packet Capture Agent Stop Script"
print_info "========================================"

if stop_application "$PID_FILE" "$FORCE_KILL"; then
    print_success "Network Packet Capture Agent stopped successfully"
else
    print_error "Failed to stop Network Packet Capture Agent"
    exit 1
fi
