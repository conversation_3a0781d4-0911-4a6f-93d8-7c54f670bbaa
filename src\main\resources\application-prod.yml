# Production environment configuration
spring:
  profiles:
    active: prod

# Production logging - more conservative
logging:
  level:
    com.bqd.netpcapagent: INFO
    org.pcap4j: WARN
    org.apache.rocketmq: WARN
    org.springframework: WARN
    org.springframework.boot.actuate: WARN
  file:
    name: logs/net-pcap-agent.log
    max-size: 500MB
    max-history: 60  # Keep 60 days of logs

# Production-optimized packet capture settings
packet-capture:
  host-ip: ${HOST_IP:127.0.0.1}
  default-port: ${DEFAULT_PORT:80}
  
  buffer:
    max-size: 50000  # Larger buffer for production
    cleanup-interval: 120
    max-age: 600
  
  reassembly:
    default-confirm-duration: 10  # More conservative timing
    max-wait-time: 60
    batch-size: 200  # Larger batches for efficiency
  
  performance:
    worker-threads: 8  # More threads for production
    pcap-buffer-size: 131072  # Larger pcap buffer
    pcap-timeout: 2000

# Production RocketMQ settings
rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:net-pcap-prod}
    send-message-timeout: 5000
    retry-times-when-send-failed: 5  # More retries for reliability
    retry-times-when-send-async-failed: 5
    max-message-size: 8388608  # 8MB max message size

# Restricted actuator endpoints for production
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true
